/**
 * 增強訂單確認對話框
 * 解決 USI AIOS 價格幻覺問題
 */

// 保存原始的函數（如果存在）
const originalShowOrderConfirmationDialog = window.showOrderConfirmationDialog || null;

// 創建一個自執行的異步函數以允許在頂層使用 await
(async function() {
    try {
        // 檢查 proceedToCheckout 是否已經存在
        if (typeof window.proceedToCheckout !== 'function') {
            console.log('未檢測到 checkout-fix.js 中的 proceedToCheckout 函數，啟用備用邏輯');
            
            // 增強的訂單確認對話框函數 - 僅在缺少 proceedToCheckout 時使用
            window.showOrderConfirmationDialog = async function(aiResponse) {
                console.log('顯示備用的訂單確認對話框，AI 回應:', aiResponse);
                
                // 準備訂單摘要內容
                const modalOrderSummary = document.getElementById('modal-order-summary');
                const modalTotalAmount = document.getElementById('modal-total-amount');
                
                // 從當前的 Gemini 回應中獲取訂單信息
                const orderResultContainer = document.getElementById('order-result-container');
                let summaryHtml = '';
                let finalTotal = 0;
                let orderItems = [];
                
                // 優先使用傳入的 aiResponse 參數，然後使用存儲的修正回應
                const responseText = aiResponse || 
                                    window.currentCorrectedResponse || 
                                    (orderResultContainer && orderResultContainer.querySelector('.response-content')) ? 
                                    orderResultContainer.querySelector('.response-content').textContent : '';
                
                if (responseText) {
                    console.log('使用增強的價格校驗工具處理訂單...');
                    
                    try {
                        // 1. 首先，從回應文本中提取原始總金額（用於對比）
                        const originalTotal = window.menuPriceValidator.extractTotalAmount(responseText);
                        console.log('從原始回應中提取的總金額:', originalTotal);
                        
                        // 2. 使用菜單價格校驗工具提取訂單項目和正確價格
                        const menuItems = await window.menuPriceValidator.extractMenuItemsWithCorrectPrices(responseText);
                        console.log('提取到的菜單項目:', menuItems);
                        
                        if (menuItems.length > 0) {
                            // 3. 使用正確的價格計算總金額
                            finalTotal = window.menuPriceValidator.calculateOrderTotal(menuItems);
                            console.log('基於真實菜單價格計算的總金額:', finalTotal);
                            
                            // 如果原始總金額與計算總金額差異較大，顯示警告
                            if (originalTotal > 0 && Math.abs(originalTotal - finalTotal) > 10) {
                                console.warn(`總金額差異較大！AI回應: NT$${originalTotal}，計算得出: NT$${finalTotal}`);
                                showToastMessage(`已修正總金額從 NT$${originalTotal} 到 NT$${finalTotal}`, 'warning');
                            }
                            
                            // 簡化顯示所有項目
                            summaryHtml = menuItems.map(item => 
                                `${item.name} * ${item.quantity}`
                            ).join('<br>');
                            
                            // 保存訂單項目以便後續處理
                            orderItems = menuItems;
                        } else {
                            // 如果沒有提取到項目，使用替代方法提取總金額
                            console.log('未提取到菜單項目，使用替代方法提取總金額...');
                            
                            // 使用 extractTotalAmount 作為備用方法
                            if (originalTotal > 0) {
                                finalTotal = originalTotal;
                                console.log('使用原始提取的總金額:', finalTotal);
                            }
                            
                            // 使用簡化摘要
                            const lines = responseText.split('\n');
                            const firstLine = lines && lines.length > 0 ? (lines[0] || '根據 AI 分析的訂單內容') : '根據 AI 分析的訂單內容';
                            summaryHtml = firstLine;
                        }
                    } catch (error) {
                        console.error('處理訂單數據時出錯:', error);
                        
                        // 出錯時嘗試使用基本方法提取總金額
                        try {
                            finalTotal = window.menuPriceValidator.extractTotalAmount(responseText);
                            console.log('使用基本方法提取的總金額:', finalTotal);
                            
                            // 使用簡化摘要
                            summaryHtml = 'USI AIOS 分析的訂單內容';
                        } catch (innerError) {
                            console.error('提取總金額時出錯:', innerError);
                            finalTotal = 0;
                        }
                    }
                }
                
                // 如果沒有找到具體的訂單信息，使用預設內容
                if (!summaryHtml) {
                    summaryHtml = 'USI AIOS 已為您分析的訂單';
                }
                
                // 更新模態框內容
                if (modalOrderSummary) {
                    modalOrderSummary.innerHTML = summaryHtml;
                    console.log('更新訂單摘要內容:', summaryHtml);
                }
                
                // 最後確認總金額是否合理
                if (finalTotal <= 0 && responseText) {
                    // 最後嘗試從回應文本中提取價格（支援 NT$ 和 ¥）
                    const allPrices = [...responseText.matchAll(/(?:NT\$|¥)(\d+)/gi)]
                        .map(match => parseInt(match[1], 10))
                        .filter(price => price > 0);
                    
                    if (allPrices.length > 0) {
                        // 如果訂單項目為空，使用最大價格作為總金額
                        if (orderItems.length === 0) {
                            finalTotal = Math.max(...allPrices);
                            console.log('最終確認 - 使用最大價格作為總金額:', finalTotal);
                        } else {
                            // 否則重新計算總金額
                            finalTotal = orderItems.reduce((total, item) => {
                                return total + (item.price * item.quantity);
                            }, 0);
                            console.log('最終確認 - 使用計算得出的總金額:', finalTotal);
                        }
                    }
                }
                
                // 更新總金額顯示
                if (modalTotalAmount) {
                    // 根據當前語言設定決定貨幣符號
                    function getCurrencySymbolByLanguage() {
                        const currentLanguage = getCurrentLanguage();
                        switch (currentLanguage) {
                            case 'ja-JP':
                                return '¥';
                            case 'en-US':
                                return 'NT$';  // 英文介面也使用 NT$ 避免與美元混淆
                            case 'zh-TW':
                            default:
                                return 'NT$';
                        }
                    }

                    let currencySymbol = getCurrencySymbolByLanguage();
                    modalTotalAmount.textContent = finalTotal > 0 ? `${currencySymbol}${finalTotal}` : `${currencySymbol}0`;
                    console.log('最終顯示的總金額:', modalTotalAmount.textContent);
                }
                
                // 顯示訂單確認模態框
                const modal = document.getElementById('order-confirmation-modal');
                if (modal) {
                    modal.style.display = 'flex';
                    console.log('訂單確認提示窗口已顯示');
                    
                    // 播放確認音效或提示
                    if (window.speakText) {
            window.speakText(getTranslation('please_confirm_your_order'));
        }
                }
            };
        } else {
            console.log('檢測到 checkout-fix.js 中的 proceedToCheckout 函數，備用邏輯不啟用');
            console.log('所有訂單確認功能將由 checkout-fix.js 提供');
            
            // 確保 confirmGeminiOrder 函數使用 proceedToCheckout
            if (typeof window.confirmGeminiOrder === 'function') {
                const originalConfirmGeminiOrder = window.confirmGeminiOrder;
                window.confirmGeminiOrder = function() {
                    console.log('使用修復版的結帳功能確認訂單');
                    window.proceedToCheckout();
                };
                console.log('已覆寫 confirmGeminiOrder 函數，確保直接使用 proceedToCheckout');
            }
            
            // 不繼續執行下面的備用邏輯
            console.log('order-confirmation-fix.js 的備用邏輯已被禁用');
        }
          console.log('訂單確認對話框增強模組已加載');
    } catch (error) {
        console.error('訂單確認對話框增強模組加載失敗:', error);
    }
})();
